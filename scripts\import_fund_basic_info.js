/**
 * 基金基本信息数据导入脚本
 * 从JSON文件读取基金基本信息数据并导入到PostgreSQL数据库
 * 支持批量插入和数据清理功能
 * 
 * <AUTHOR>
 * @created 2025-07-29 17:30:00
 * @updated 2025-07-29 17:30:00
 * @description 将解析后的基金基本信息JSON数据批量导入到ths_fund_basic_info表中
 */

const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
// DATABASE_URL=*******************************************************/starlink_tonghuasun_data
/**
 * PostgreSQL数据库连接配置
 * 请根据实际数据库配置修改以下参数
 */
const DB_CONFIG = {
    user: 'starlink_dev',           // 数据库用户名
    host: '***************',          // 数据库主机地址
    database: 'starlink_tonghuasun_data',    // 数据库名称
    password: 'dev_2706',  // 数据库密码
    port: 5432,                 // 数据库端口
    max: 20,                    // 连接池最大连接数
    idleTimeoutMillis: 30000,   // 空闲连接超时时间
    connectionTimeoutMillis: 2000, // 连接超时时间
};

/**
 * 数据库连接池
 */
const pool = new Pool(DB_CONFIG);

/**
 * 批量插入的批次大小
 */
const BATCH_SIZE = 1000;

/**
 * 数据库表名
 */
const TABLE_NAME = 'ths_fund_basic_info';

/**
 * 数据库字段映射
 * 将JSON字段映射到数据库字段
 */
const DB_FIELD_MAPPING = {
    fund_code: 'fundCode',
    fund_name: 'fundName',
    investment_type: 'investmentType',
    establishment_date: 'establishmentDate',
    tracking_index_name: 'trackingIndexName',
    management_company_name: 'managementCompanyName',
    manager_code_1st: 'managerCode1st',
    manager_name_1st: 'managerName1st',
    manager_code_2nd: 'managerCode2nd',
    manager_name_2nd: 'managerName2nd',
    related_fund_code: 'relatedFundCode',
    net_asset_value: 'netAssetValue',
    unit_net_value: 'unitNetValue',
    closing_price: 'closingPrice',
    management_fee_rate: 'managementFeeRate',
    price_change_pct: 'priceChangePct',
    custody_fee_rate: 'custodyFeeRate',
    trading_volume: 'tradingVolume',
    turnover_rate: 'turnoverRate',
    premium_discount_rate: 'premiumDiscountRate',
    table_updated_at: 'tableUpdatedAt'
};

/**
 * 数据类型转换函数
 * @param {any} value - 原始值
 * @param {string} fieldType - 字段类型
 * @returns {any} 转换后的值
 */
function convertValue(value, fieldType) {
    if (value === null || value === undefined || value === '') {
        return null;
    }

    switch (fieldType) {
        case 'date':
            if (typeof value === 'string') {
                const date = new Date(value);
                return isNaN(date.getTime()) ? null : date.toISOString().split('T')[0];
            }
            return null;
        
        case 'decimal':
            if (typeof value === 'number') {
                return value;
            }
            if (typeof value === 'string') {
                const num = parseFloat(value);
                return isNaN(num) ? null : num;
            }
            return null;
        
        case 'bigint':
            if (typeof value === 'number') {
                return Math.floor(value);
            }
            if (typeof value === 'string') {
                const num = parseInt(value, 10);
                return isNaN(num) ? null : num;
            }
            return null;
        
        case 'timestamp':
            if (typeof value === 'string') {
                const date = new Date(value);
                return isNaN(date.getTime()) ? null : date.toISOString();
            }
            return new Date().toISOString();
        
        default:
            return value;
    }
}

/**
 * 转换JSON记录为数据库记录
 * @param {Object} jsonRecord - JSON记录
 * @returns {Object} 数据库记录
 */
function transformToDbRecord(jsonRecord) {
    const dbRecord = {};
    
    // 字段类型定义
    const fieldTypes = {
        fund_code: 'string',
        fund_name: 'string',
        investment_type: 'string',
        establishment_date: 'date',
        tracking_index_name: 'string',
        management_company_name: 'string',
        manager_code_1st: 'string',
        manager_name_1st: 'string',
        manager_code_2nd: 'string',
        manager_name_2nd: 'string',
        related_fund_code: 'string',
        net_asset_value: 'decimal',
        unit_net_value: 'decimal',
        closing_price: 'decimal',
        management_fee_rate: 'decimal',
        price_change_pct: 'decimal',
        custody_fee_rate: 'decimal',
        trading_volume: 'bigint',
        turnover_rate: 'decimal',
        premium_discount_rate: 'decimal',
        table_updated_at: 'timestamp'
    };

    // 转换字段
    for (const [dbField, jsonField] of Object.entries(DB_FIELD_MAPPING)) {
        const fieldType = fieldTypes[dbField];
        dbRecord[dbField] = convertValue(jsonRecord[jsonField], fieldType);
    }

    return dbRecord;
}

/**
 * 生成批量插入SQL语句
 * @param {Array} records - 记录数组
 * @returns {Object} SQL语句和参数
 */
function generateBatchInsertSQL(records) {
    if (records.length === 0) {
        return { sql: '', values: [] };
    }

    const fields = Object.keys(DB_FIELD_MAPPING);
    const fieldList = fields.join(', ');
    
    const valuePlaceholders = [];
    const values = [];
    
    records.forEach((record, recordIndex) => {
        const recordPlaceholders = [];
        fields.forEach((field, fieldIndex) => {
            const paramIndex = recordIndex * fields.length + fieldIndex + 1;
            recordPlaceholders.push(`$${paramIndex}`);
            values.push(record[field]);
        });
        valuePlaceholders.push(`(${recordPlaceholders.join(', ')})`);
    });

    const sql = `
        INSERT INTO ${TABLE_NAME} (${fieldList})
        VALUES ${valuePlaceholders.join(', ')}
        ON CONFLICT (fund_code) DO UPDATE SET
            ${fields.filter(f => f !== 'fund_code').map(f => `${f} = EXCLUDED.${f}`).join(', ')}
    `;

    return { sql, values };
}

/**
 * 批量插入数据到数据库
 * @param {Array} records - 记录数组
 * @returns {Promise<number>} 插入的记录数
 */
async function batchInsertRecords(records) {
    if (records.length === 0) {
        return 0;
    }

    const client = await pool.connect();
    let insertedCount = 0;

    try {
        await client.query('BEGIN');

        // 分批处理
        for (let i = 0; i < records.length; i += BATCH_SIZE) {
            const batch = records.slice(i, i + BATCH_SIZE);
            const { sql, values } = generateBatchInsertSQL(batch);
            
            if (sql) {
                await client.query(sql, values);
                insertedCount += batch.length;
                console.log(`已处理 ${insertedCount}/${records.length} 条记录`);
            }
        }

        await client.query('COMMIT');
        console.log('数据导入事务提交成功');
        
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('数据导入失败，事务已回滚:', error.message);
        throw error;
    } finally {
        client.release();
    }

    return insertedCount;
}

/**
 * 清空表数据
 * @returns {Promise<void>}
 */
async function clearTable() {
    const client = await pool.connect();
    try {
        await client.query(`TRUNCATE TABLE ${TABLE_NAME} RESTART IDENTITY CASCADE`);
        console.log(`表 ${TABLE_NAME} 已清空`);
    } catch (error) {
        console.error('清空表失败:', error.message);
        throw error;
    } finally {
        client.release();
    }
}

/**
 * 检查数据库连接
 * @returns {Promise<boolean>} 连接是否成功
 */
async function checkDatabaseConnection() {
    try {
        const client = await pool.connect();
        await client.query('SELECT 1');
        client.release();
        console.log('数据库连接成功');
        return true;
    } catch (error) {
        console.error('数据库连接失败:', error.message);
        console.error('请检查数据库配置和连接参数');
        return false;
    }
}

/**
 * 获取表记录统计
 * @returns {Promise<number>} 记录总数
 */
async function getTableCount() {
    const client = await pool.connect();
    try {
        const result = await client.query(`SELECT COUNT(*) as count FROM ${TABLE_NAME}`);
        return parseInt(result.rows[0].count, 10);
    } catch (error) {
        console.error('获取表记录数失败:', error.message);
        return 0;
    } finally {
        client.release();
    }
}

/**
 * 主导入函数
 * @param {string} jsonFilePath - JSON文件路径
 * @param {boolean} clearFirst - 是否先清空表
 * @returns {Promise<void>}
 */
async function importFundBasicInfo(jsonFilePath, clearFirst = false) {
    console.log('='.repeat(60));
    console.log('基金基本信息数据导入开始');
    console.log(`导入时间: ${new Date().toLocaleString()}`);
    console.log('='.repeat(60));

    try {
        // 检查数据库连接
        const isConnected = await checkDatabaseConnection();
        if (!isConnected) {
            throw new Error('数据库连接失败，请检查配置');
        }

        // 检查JSON文件是否存在
        if (!fs.existsSync(jsonFilePath)) {
            throw new Error(`JSON文件不存在: ${jsonFilePath}`);
        }

        // 读取JSON数据
        console.log(`读取JSON文件: ${jsonFilePath}`);
        const jsonData = JSON.parse(fs.readFileSync(jsonFilePath, 'utf8'));
        console.log(`JSON文件记录数: ${jsonData.length}`);

        // 获取导入前的记录数
        const beforeCount = await getTableCount();
        console.log(`导入前表记录数: ${beforeCount}`);

        // 是否清空表
        if (clearFirst) {
            console.log('清空表数据...');
            await clearTable();
        }

        // 转换数据格式
        console.log('开始数据格式转换...');
        const dbRecords = jsonData.map((record, index) => {
            try {
                return transformToDbRecord(record);
            } catch (error) {
                console.error(`转换第${index + 1}条记录失败:`, error.message);
                return null;
            }
        }).filter(record => record !== null);

        console.log(`成功转换记录数: ${dbRecords.length}`);

        // 批量导入数据
        console.log('开始批量导入数据...');
        const insertedCount = await batchInsertRecords(dbRecords);

        // 获取导入后的记录数
        const afterCount = await getTableCount();
        console.log(`导入后表记录数: ${afterCount}`);

        console.log('='.repeat(60));
        console.log('数据导入完成');
        console.log(`成功导入记录数: ${insertedCount}`);
        console.log(`完成时间: ${new Date().toLocaleString()}`);
        console.log('='.repeat(60));

    } catch (error) {
        console.error('数据导入过程中发生错误:', error.message);
        console.error('错误详情:', error);
        throw error;
    } finally {
        // 关闭数据库连接池
        await pool.end();
    }
}

/**
 * 主执行函数
 */
async function main() {
    try {
        // 固定使用data目录下的fund_basic_info.json文件
        const jsonFilePath = path.join(process.cwd(), 'data', 'fund_basic_info.json');

        console.log('='.repeat(60));
        console.log('基金基本信息数据导入测试');
        console.log(`当前时间: ${new Date().toLocaleString()}`);
        console.log('='.repeat(60));

        // 检查文件是否存在
        if (!fs.existsSync(jsonFilePath)) {
            console.error(`❌ 数据文件不存在: ${jsonFilePath}`);
            console.log('请确保已运行Excel解析脚本生成JSON文件');
            process.exit(1);
        }

        // 显示数据库配置信息（隐藏密码）
        console.log('数据库配置:');
        console.log(`  主机: ${DB_CONFIG.host}:${DB_CONFIG.port}`);
        console.log(`  数据库: ${DB_CONFIG.database}`);
        console.log(`  用户: ${DB_CONFIG.user}`);
        console.log(`  表名: ${TABLE_NAME}`);
        console.log('');

        // 导入数据（不清空表）
        await importFundBasicInfo(jsonFilePath, false);

    } catch (error) {
        console.error('程序执行失败:', error.message);
        process.exit(1);
    }
}

// 执行脚本
if (require.main === module) {
    main();
}

module.exports = {
    importFundBasicInfo,
    transformToDbRecord,
    convertValue,
    checkDatabaseConnection,
    clearTable,
    getTableCount
};
