/**
 * Excel文件解析脚本 - 解析基金行业配置信息表
 * 使用xlsx库解析fund_to_industry.xlsx文件并转换为数据库模型格式
 *
 * <AUTHOR>
 * @created 2025-01-27 14:30:00
 * @updated 2025-01-27 14:30:00
 * @description 解析基金行业配置Excel文件，输出完整数据并保存为JSON文件，字段映射遵循TonghuashunFundIndustryInfo模型规范
 */

const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs');

/**
 * 字段映射配置 - Excel字段名到数据库字段名的映射
 * 遵循TonghuashunFundIndustryInfo模型规范
 * @type {Object}
 */
const FIELD_MAPPING = {
    '代码': 'fundCode',
    '名称': 'fundName',
    '行业代码': 'industryCode',
    '行业名称': 'industryName',
    '市值(万元)': 'marketValue',
    '占净值比(%)': 'netValueRatio',
    '占股票投资市值比(%)': 'stockInvestRatio',
    '股票市场标准行业配置比例(%)': 'standardAllocationRatio',
    '相对标准行业配置比例(%)': 'relativeAllocationRatio',
    '市值增长率(%)': 'marketValueGrowthRate',
    '投资类型': 'investmentType',
    '管理公司': 'managementCompany'
};

/**
 * 数据清洗和转换函数
 * @param {any} value - 原始值
 * @param {string} fieldName - 字段名
 * @returns {any} 清洗后的值
 */
function cleanValue(value, fieldName) {
    // 处理空值和"--"
    if (value === null || value === undefined || value === '--' || value === '') {
        return null;
    }

    // 数值字段处理（市值和各类比例）
    const numericFields = [
        'marketValue', 'netValueRatio', 'stockInvestRatio',
        'standardAllocationRatio', 'relativeAllocationRatio', 'marketValueGrowthRate'
    ];
    
    if (numericFields.includes(fieldName)) {
        if (typeof value === 'number') {
            return value;
        }
        if (typeof value === 'string') {
            // 移除千分位分隔符
            const cleanedValue = value.replace(/,/g, '');
            const num = parseFloat(cleanedValue);
            return isNaN(num) ? null : num;
        }
        return null;
    }

    // 字符串字段处理
    return typeof value === 'string' ? value.trim() : String(value);
}

/**
 * 转换单条记录为数据库模型格式
 * @param {Object} record - Excel原始记录
 * @returns {Object} 转换后的记录
 */
function transformRecord(record) {
    const transformed = {};
    
    // 遍历字段映射，转换数据
    for (const [excelField, dbField] of Object.entries(FIELD_MAPPING)) {
        const rawValue = record[excelField];
        transformed[dbField] = cleanValue(rawValue, dbField);
    }
    
    // 添加表更新时间
    transformed.updateTime = new Date().toISOString();
    
    return transformed;
}

/**
 * 确保目录存在
 * @param {string} dirPath - 目录路径
 */
function ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`创建目录: ${dirPath}`);
    }
}

/**
 * 解析Excel文件并输出完整数据为JSON格式
 * @param {string} filePath - Excel文件路径
 */
function parseExcelFileAll(filePath) {
    try {
        // 检查文件是否存在
        if (!fs.existsSync(filePath)) {
            console.error(`文件不存在: ${filePath}`);
            return;
        }

        console.log(`开始解析文件: ${filePath}`);
        console.log(`解析时间: ${new Date().toLocaleString()}`);
        console.log('='.repeat(50));

        // 读取Excel文件
        const workbook = XLSX.readFile(filePath);
        
        // 获取第一个工作表名称
        const sheetName = workbook.SheetNames[0];
        console.log(`工作表名称: ${sheetName}`);
        
        // 获取工作表数据
        const worksheet = workbook.Sheets[sheetName];
        
        // 将工作表转换为JSON格式，从第2行开始（跳过标题行）
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { range: 1 });
        
        console.log(`总记录数: ${jsonData.length}`);
        console.log('='.repeat(50));
        
        // 显示前10条原始数据
        console.log('前10条原始数据:');
        console.log('='.repeat(50));
        console.log(JSON.stringify(jsonData.slice(0, 10), null, 2));
        console.log('='.repeat(50));
        
        // 转换所有数据
        console.log('开始转换所有数据...');

        const transformedData = jsonData.map((record, index) => {
            try {
                return transformRecord(record);
            } catch (error) {
                console.error(`转换第${index + 1}条记录时出错:`, error.message);
                return null;
            }
        }).filter(record => record !== null);
        
        console.log(`成功转换记录数: ${transformedData.length}`);

        // 显示转换后的前3条数据作为预览
        console.log('\n转换后的前3条数据预览:');
        console.log('='.repeat(50));
        console.log(JSON.stringify(transformedData.slice(0, 3), null, 2));
        
        // 确保data目录存在
        const dataDir = path.join(process.cwd(), 'data');
        ensureDirectoryExists(dataDir);
        
        // 保存转换后的数据
        const outputPath = path.join(dataDir, 'fund_industry_info.json');
        fs.writeFileSync(outputPath, JSON.stringify(transformedData, null, 2), 'utf8');
        
        console.log(`\n数据已保存到: ${outputPath}`);
        console.log(`文件大小: ${(fs.statSync(outputPath).size / 1024 / 1024).toFixed(2)} MB`);
        console.log('\n解析完成！');
        
    } catch (error) {
        console.error('解析Excel文件时发生错误:', error.message);
        console.error('错误详情:', error);
    }
}

// 主执行函数
function main() {
    const filePath = path.join(process.cwd(), 'fund_to_industry.xlsx');
    parseExcelFileAll(filePath);
}

// 执行脚本
if (require.main === module) {
    main();
}

module.exports = { parseExcelFileAll, transformRecord, cleanValue };
