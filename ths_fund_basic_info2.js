/**
 * Excel文件解析脚本 - 解析基金业绩回报信息表
 * 使用xlsx库解析ths_fund_basic_info2.xlsx文件并转换为数据库模型格式
 *
 * <AUTHOR>
 * @created 2025-07-29 16:47:31
 * @updated 2025-07-29 16:47:31
 * @description 解析基金业绩回报Excel文件，输出完整数据并保存为JSON文件，字段映射遵循FundPerformanceReturn模型规范
 */

const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs');

/**
 * 字段映射配置 - Excel字段名到数据库字段名的映射
 * 遵循FundPerformanceReturn模型规范
 * @type {Object}
 */
const FIELD_MAPPING = {
    '基金代码': 'fundCode',
    '基金简称': 'fundName',
    '最新日期': 'latestDate',
    '上期日期': 'previousDate',
    '上期单位净值': 'previousUnitNetValue',
    '今年以来总回报(%)': 'ytdTotalReturn',
    '最近一周总回报(%)': 'week1TotalReturn',
    '最近一月总回报(%)': 'month1TotalReturn',
    '最近三月总回报(%)': 'month3TotalReturn',
    '最近六月总回报(%)': 'month6TotalReturn',
    '最近一年总回报(%)': 'year1TotalReturn',
    '最近两年总回报(%)': 'year2TotalReturn',
    '最近三年总回报(%)': 'year3TotalReturn',
    '最近五年总回报(%)': 'year5TotalReturn',
    '成立以来总回报(%)': 'sinceInceptionReturn'
};

/**
 * 数据清洗和转换函数
 * @param {any} value - 原始值
 * @param {string} fieldName - 字段名
 * @returns {any} 清洗后的值
 */
function cleanValue(value, fieldName) {
    // 处理空值和"--"
    if (value === null || value === undefined || value === '--' || value === '') {
        return null;
    }

    // 日期字段处理
    if (fieldName === 'latestDate' || fieldName === 'previousDate') {
        if (typeof value === 'string') {
            const date = new Date(value);
            return isNaN(date.getTime()) ? null : date.toISOString().split('T')[0];
        }
        return null;
    }

    // 数值字段处理（回报率和净值）
    const numericFields = [
        'previousUnitNetValue', 'ytdTotalReturn', 'week1TotalReturn',
        'month1TotalReturn', 'month3TotalReturn', 'month6TotalReturn',
        'year1TotalReturn', 'year2TotalReturn', 'year3TotalReturn',
        'year5TotalReturn', 'sinceInceptionReturn'
    ];
    
    if (numericFields.includes(fieldName)) {
        if (typeof value === 'number') {
            return value;
        }
        if (typeof value === 'string') {
            const num = parseFloat(value);
            return isNaN(num) ? null : num;
        }
        return null;
    }

    // 字符串字段处理
    return typeof value === 'string' ? value.trim() : String(value);
}

/**
 * 转换单条记录为数据库模型格式
 * @param {Object} record - Excel原始记录
 * @returns {Object} 转换后的记录
 */
function transformRecord(record) {
    const transformed = {};
    
    // 遍历字段映射，转换数据
    for (const [excelField, dbField] of Object.entries(FIELD_MAPPING)) {
        const rawValue = record[excelField];
        transformed[dbField] = cleanValue(rawValue, dbField);
    }
    
    // 添加表更新时间
    transformed.tableUpdatedAt = new Date().toISOString();
    
    return transformed;
}

/**
 * 确保目录存在
 * @param {string} dirPath - 目录路径
 */
function ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`创建目录: ${dirPath}`);
    }
}

/**
 * 解析Excel文件并输出完整数据为JSON格式
 * @param {string} filePath - Excel文件路径
 */
function parseExcelFileAll(filePath) {
    try {
        // 检查文件是否存在
        if (!fs.existsSync(filePath)) {
            console.error(`文件不存在: ${filePath}`);
            return;
        }

        console.log(`开始解析文件: ${filePath}`);
        console.log(`解析时间: ${new Date().toLocaleString()}`);
        console.log('='.repeat(50));

        // 读取Excel文件
        const workbook = XLSX.readFile(filePath);
        
        // 获取第一个工作表名称
        const sheetName = workbook.SheetNames[0];
        console.log(`工作表名称: ${sheetName}`);
        
        // 获取工作表数据
        const worksheet = workbook.Sheets[sheetName];
        
        // 将工作表转换为JSON格式
        const jsonData = XLSX.utils.sheet_to_json(worksheet);
        
        console.log(`总记录数: ${jsonData.length}`);
        console.log('='.repeat(50));
        
        // 转换所有数据
        console.log('开始转换所有数据...');

        const transformedData = jsonData.map((record, index) => {
            try {
                return transformRecord(record);
            } catch (error) {
                console.error(`转换第${index + 1}条记录时出错:`, error.message);
                return null;
            }
        }).filter(record => record !== null);
        
        console.log(`成功转换记录数: ${transformedData.length}`);

        // 显示转换后的前3条数据作为预览
        console.log('\n转换后的前3条数据预览:');
        console.log('='.repeat(50));
        console.log(JSON.stringify(transformedData.slice(0, 3), null, 2));
        
        // 确保data目录存在
        const dataDir = path.join(process.cwd(), 'data');
        ensureDirectoryExists(dataDir);
        
        // 保存转换后的数据
        const outputPath = path.join(dataDir, 'ths_fund_basic_info2.json');
        fs.writeFileSync(outputPath, JSON.stringify(transformedData, null, 2), 'utf8');
        
        console.log(`\n数据已保存到: ${outputPath}`);
        console.log(`文件大小: ${(fs.statSync(outputPath).size / 1024 / 1024).toFixed(2)} MB`);
        console.log('\n解析完成！');
        
    } catch (error) {
        console.error('解析Excel文件时发生错误:', error.message);
        console.error('错误详情:', error);
    }
}

// 主执行函数
function main() {
    const filePath = path.join(process.cwd(), 'ths_fund_basic_info2.xlsx');
    parseExcelFileAll(filePath);
}

// 执行脚本
if (require.main === module) {
    main();
}

module.exports = { parseExcelFileAll, transformRecord, cleanValue };
