/**
 * 基金行业配置数据库导入脚本 - 简化版
 * 将fund_industry_info.json数据导入到tonghuashun_fund_industry_info表
 * 不依赖额外的npm包，使用原生Node.js和MySQL
 *
 * <AUTHOR>
 * @created 2025-08-04 10:55:00
 * @updated 2025-08-04 10:55:00
 * @description 简化版数据库导入脚本，生成SQL文件供手动执行
 */

const fs = require('fs');
const path = require('path');

/**
 * 配置参数
 * @type {Object}
 */
const CONFIG = {
    batchSize: 1000,           // 每个SQL文件的记录数
    outputDir: 'sql_output',   // SQL文件输出目录
    tableName: 'tonghuashun_fund_industry_info'
};

/**
 * 确保目录存在
 * @param {string} dirPath - 目录路径
 */
function ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`✅ 创建目录: ${dirPath}`);
    }
}

/**
 * 转义SQL字符串
 * @param {any} value - 要转义的值
 * @returns {string} 转义后的SQL值
 */
function escapeSqlValue(value) {
    if (value === null || value === undefined) {
        return 'NULL';
    }
    
    if (typeof value === 'number') {
        return isNaN(value) ? 'NULL' : value.toString();
    }
    
    if (typeof value === 'boolean') {
        return value ? '1' : '0';
    }
    
    if (value instanceof Date) {
        return `'${value.toISOString().slice(0, 19).replace('T', ' ')}'`;
    }
    
    // 字符串转义
    const escaped = String(value)
        .replace(/\\/g, '\\\\')
        .replace(/'/g, "\\'")
        .replace(/"/g, '\\"')
        .replace(/\n/g, '\\n')
        .replace(/\r/g, '\\r')
        .replace(/\t/g, '\\t');
    
    return `'${escaped}'`;
}

/**
 * 数据验证和清洗
 * @param {Object} record - 原始记录
 * @returns {Object|null} 清洗后的记录
 */
function validateAndCleanRecord(record) {
    try {
        // 必填字段检查
        if (!record.fundCode || !record.industryCode || !record.fundName || !record.industryName) {
            console.warn('⚠️ 跳过无效记录：缺少必填字段', {
                fundCode: record.fundCode,
                industryCode: record.industryCode
            });
            return null;
        }

        // 数据清洗和转换
        const cleanedRecord = {
            fund_code: String(record.fundCode).trim(),
            industry_code: String(record.industryCode).trim(),
            fund_name: String(record.fundName).trim(),
            industry_name: String(record.industryName).trim(),
            market_value: record.marketValue !== null ? Number(record.marketValue) : null,
            net_value_ratio: record.netValueRatio !== null ? Number(record.netValueRatio) : null,
            stock_invest_ratio: record.stockInvestRatio !== null ? Number(record.stockInvestRatio) : null,
            standard_allocation_ratio: record.standardAllocationRatio !== null ? Number(record.standardAllocationRatio) : null,
            relative_allocation_ratio: record.relativeAllocationRatio !== null ? Number(record.relativeAllocationRatio) : null,
            market_value_growth_rate: record.marketValueGrowthRate !== null ? Number(record.marketValueGrowthRate) : null,
            investment_type: record.investmentType ? String(record.investmentType).trim() : null,
            management_company: record.managementCompany ? String(record.managementCompany).trim() : null,
            update_time: new Date(record.updateTime)
        };

        return cleanedRecord;
    } catch (error) {
        console.warn('⚠️ 数据清洗失败:', error.message);
        return null;
    }
}

/**
 * 生成创建表的SQL
 * @returns {string} 创建表的SQL语句
 */
function generateCreateTableSQL() {
    return `-- 创建基金行业配置表
DROP TABLE IF EXISTS ${CONFIG.tableName};

CREATE TABLE ${CONFIG.tableName} (
    fund_code VARCHAR(50) NOT NULL COMMENT '基金代码',
    industry_code VARCHAR(50) NOT NULL COMMENT '行业代码',
    fund_name VARCHAR(200) NOT NULL COMMENT '基金名称',
    industry_name VARCHAR(200) NOT NULL COMMENT '行业名称',
    market_value DECIMAL(15,2) NULL COMMENT '市值(万元)',
    net_value_ratio DECIMAL(10,4) NULL COMMENT '占净值比(%)',
    stock_invest_ratio DECIMAL(10,4) NULL COMMENT '占股票投资市值比(%)',
    standard_allocation_ratio DECIMAL(10,4) NULL COMMENT '股票市场标准行业配置比例(%)',
    relative_allocation_ratio DECIMAL(10,4) NULL COMMENT '相对标准行业配置比例(%)',
    market_value_growth_rate DECIMAL(10,4) NULL COMMENT '市值增长率(%)',
    investment_type VARCHAR(100) NULL COMMENT '投资类型',
    management_company VARCHAR(200) NULL COMMENT '管理公司',
    update_time DATETIME NOT NULL COMMENT '表格更新时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    PRIMARY KEY (fund_code, industry_code),
    INDEX idx_fund_code (fund_code),
    INDEX idx_industry_code (industry_code),
    INDEX idx_industry_name (industry_name),
    INDEX idx_management_company (management_company),
    INDEX idx_update_time (update_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='基金行业配置表';

`;
}

/**
 * 生成插入数据的SQL
 * @param {Array} records - 记录数组
 * @param {number} batchNumber - 批次号
 * @returns {string} 插入数据的SQL语句
 */
function generateInsertSQL(records, batchNumber) {
    if (records.length === 0) {
        return '';
    }

    let sql = `-- 批次 ${batchNumber}: ${records.length} 条记录\n`;
    sql += `INSERT INTO ${CONFIG.tableName} (\n`;
    sql += `    fund_code, industry_code, fund_name, industry_name,\n`;
    sql += `    market_value, net_value_ratio, stock_invest_ratio,\n`;
    sql += `    standard_allocation_ratio, relative_allocation_ratio,\n`;
    sql += `    market_value_growth_rate, investment_type, management_company,\n`;
    sql += `    update_time\n`;
    sql += `) VALUES\n`;

    const values = records.map((record, index) => {
        const valueStr = `(${[
            escapeSqlValue(record.fund_code),
            escapeSqlValue(record.industry_code),
            escapeSqlValue(record.fund_name),
            escapeSqlValue(record.industry_name),
            escapeSqlValue(record.market_value),
            escapeSqlValue(record.net_value_ratio),
            escapeSqlValue(record.stock_invest_ratio),
            escapeSqlValue(record.standard_allocation_ratio),
            escapeSqlValue(record.relative_allocation_ratio),
            escapeSqlValue(record.market_value_growth_rate),
            escapeSqlValue(record.investment_type),
            escapeSqlValue(record.management_company),
            escapeSqlValue(record.update_time)
        ].join(', ')})`;
        
        return valueStr + (index === records.length - 1 ? '' : ',');
    });

    sql += values.join('\n');
    sql += `\nON DUPLICATE KEY UPDATE\n`;
    sql += `    fund_name = VALUES(fund_name),\n`;
    sql += `    industry_name = VALUES(industry_name),\n`;
    sql += `    market_value = VALUES(market_value),\n`;
    sql += `    net_value_ratio = VALUES(net_value_ratio),\n`;
    sql += `    stock_invest_ratio = VALUES(stock_invest_ratio),\n`;
    sql += `    standard_allocation_ratio = VALUES(standard_allocation_ratio),\n`;
    sql += `    relative_allocation_ratio = VALUES(relative_allocation_ratio),\n`;
    sql += `    market_value_growth_rate = VALUES(market_value_growth_rate),\n`;
    sql += `    investment_type = VALUES(investment_type),\n`;
    sql += `    management_company = VALUES(management_company),\n`;
    sql += `    update_time = VALUES(update_time),\n`;
    sql += `    updated_at = CURRENT_TIMESTAMP;\n\n`;

    return sql;
}

/**
 * 主处理函数
 * @param {string} jsonFilePath - JSON数据文件路径
 */
async function generateSQLFiles(jsonFilePath) {
    try {
        console.log('🚀 开始生成SQL导入文件');
        console.log(`处理时间: ${new Date().toLocaleString()}`);
        console.log('='.repeat(60));

        // 检查JSON文件是否存在
        if (!fs.existsSync(jsonFilePath)) {
            throw new Error(`文件不存在: ${jsonFilePath}`);
        }

        // 创建输出目录
        const outputDir = path.join(process.cwd(), CONFIG.outputDir);
        ensureDirectoryExists(outputDir);

        // 读取JSON数据
        console.log(`📖 正在读取数据文件: ${jsonFilePath}`);
        const fileContent = fs.readFileSync(jsonFilePath, 'utf8');
        const rawData = JSON.parse(fileContent);
        
        if (!Array.isArray(rawData)) {
            throw new Error('JSON文件格式错误：期望数组格式');
        }

        console.log(`✅ 成功读取 ${rawData.length} 条记录`);

        // 数据验证和清洗
        console.log('🔄 开始数据验证和清洗...');
        const validRecords = [];
        let errorCount = 0;
        
        for (let i = 0; i < rawData.length; i++) {
            const cleanedRecord = validateAndCleanRecord(rawData[i]);
            if (cleanedRecord) {
                validRecords.push(cleanedRecord);
            } else {
                errorCount++;
            }
            
            // 进度显示
            if ((i + 1) % 10000 === 0) {
                console.log(`📊 已验证 ${i + 1}/${rawData.length} 条记录`);
            }
        }

        console.log(`✅ 数据验证完成: 有效记录 ${validRecords.length} 条，无效记录 ${errorCount} 条`);

        // 生成创建表SQL文件
        const createTableSQL = generateCreateTableSQL();
        const createTablePath = path.join(outputDir, '01_create_table.sql');
        fs.writeFileSync(createTablePath, createTableSQL, 'utf8');
        console.log(`✅ 创建表SQL文件已生成: ${createTablePath}`);

        // 生成批量插入SQL文件
        console.log('🔄 开始生成插入SQL文件...');
        const batchSize = CONFIG.batchSize;
        const totalBatches = Math.ceil(validRecords.length / batchSize);
        
        for (let i = 0; i < validRecords.length; i += batchSize) {
            const batch = validRecords.slice(i, i + batchSize);
            const batchNumber = Math.floor(i / batchSize) + 1;
            
            const insertSQL = generateInsertSQL(batch, batchNumber);
            const fileName = `02_insert_data_batch_${batchNumber.toString().padStart(3, '0')}.sql`;
            const filePath = path.join(outputDir, fileName);
            
            fs.writeFileSync(filePath, insertSQL, 'utf8');
            
            const progress = ((batchNumber / totalBatches) * 100).toFixed(1);
            console.log(`📈 进度: ${progress}% - 生成文件: ${fileName}`);
        }

        // 生成执行脚本
        const executeScript = generateExecuteScript(totalBatches);
        const executeScriptPath = path.join(outputDir, '00_execute_all.sql');
        fs.writeFileSync(executeScriptPath, executeScript, 'utf8');

        console.log('='.repeat(60));
        console.log('📊 SQL文件生成完成统计:');
        console.log(`   总记录数: ${rawData.length}`);
        console.log(`   有效记录: ${validRecords.length}`);
        console.log(`   SQL文件数: ${totalBatches + 2}`);
        console.log(`   输出目录: ${outputDir}`);
        console.log('='.repeat(60));
        console.log('✅ SQL文件生成完成！');
        console.log('💡 执行方式：');
        console.log('   1. 手动执行: 依次执行生成的SQL文件');
        console.log('   2. 批量执行: mysql -u用户名 -p数据库名 < 00_execute_all.sql');

    } catch (error) {
        console.error('❌ 生成SQL文件失败:', error.message);
        throw error;
    }
}

/**
 * 生成执行脚本
 * @param {number} totalBatches - 总批次数
 * @returns {string} 执行脚本内容
 */
function generateExecuteScript(totalBatches) {
    let script = `-- 基金行业配置数据导入执行脚本\n`;
    script += `-- 生成时间: ${new Date().toLocaleString()}\n`;
    script += `-- 使用方法: mysql -u用户名 -p数据库名 < 00_execute_all.sql\n\n`;
    
    script += `-- 1. 创建表结构\n`;
    script += `SOURCE 01_create_table.sql;\n\n`;
    
    script += `-- 2. 插入数据\n`;
    for (let i = 1; i <= totalBatches; i++) {
        const fileName = `02_insert_data_batch_${i.toString().padStart(3, '0')}.sql`;
        script += `SOURCE ${fileName};\n`;
    }
    
    script += `\n-- 执行完成\n`;
    script += `SELECT '数据导入完成' AS status;\n`;
    
    return script;
}

/**
 * 主执行函数
 */
async function main() {
    try {
        // JSON文件路径
        const jsonFilePath = path.join(process.cwd(), 'data', 'fund_industry_info.json');
        
        // 开始生成SQL文件
        await generateSQLFiles(jsonFilePath);
        
    } catch (error) {
        console.error('❌ 程序执行失败:', error.message);
        process.exit(1);
    }
}

// 执行脚本
if (require.main === module) {
    main();
}

module.exports = { 
    generateSQLFiles, 
    validateAndCleanRecord,
    generateCreateTableSQL,
    generateInsertSQL 
};
