/**
 * 基金行业配置数据库导入脚本
 * 将fund_industry_info.json数据导入到tonghuashun_fund_industry_info表
 *
 * <AUTHOR>
 * @created 2025-08-04 10:50:00
 * @updated 2025-08-04 10:50:00
 * @description 批量导入基金行业配置数据到MySQL数据库，支持重复数据处理和错误恢复
 */

const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');

/**
 * 数据库配置
 * @type {Object}
 */
const DB_CONFIG = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'fund_database',
    charset: 'utf8mb4',
    timezone: '+08:00'
};

/**
 * 批量导入配置
 * @type {Object}
 */
const IMPORT_CONFIG = {
    batchSize: 1000,           // 每批处理的记录数
    maxRetries: 3,             // 最大重试次数
    retryDelay: 1000,          // 重试延迟(毫秒)
    enableDuplicateCheck: true, // 是否启用重复数据检查
    logProgress: true          // 是否记录进度日志
};

/**
 * 数据库连接池
 */
let connectionPool = null;

/**
 * 初始化数据库连接池
 * @returns {Promise<mysql.Pool>} 数据库连接池
 */
async function initializeDatabase() {
    try {
        connectionPool = mysql.createPool({
            ...DB_CONFIG,
            waitForConnections: true,
            connectionLimit: 10,
            queueLimit: 0,
            acquireTimeout: 60000,
            timeout: 60000
        });

        // 测试连接
        const connection = await connectionPool.getConnection();
        console.log('✅ 数据库连接成功');
        connection.release();
        
        return connectionPool;
    } catch (error) {
        console.error('❌ 数据库连接失败:', error.message);
        throw error;
    }
}

/**
 * 创建表（如果不存在）
 * @param {mysql.Pool} pool - 数据库连接池
 */
async function createTableIfNotExists(pool) {
    const createTableSQL = `
        CREATE TABLE IF NOT EXISTS tonghuashun_fund_industry_info (
            fund_code VARCHAR(50) NOT NULL COMMENT '基金代码',
            industry_code VARCHAR(50) NOT NULL COMMENT '行业代码',
            fund_name VARCHAR(200) NOT NULL COMMENT '基金名称',
            industry_name VARCHAR(200) NOT NULL COMMENT '行业名称',
            market_value DECIMAL(15,2) NULL COMMENT '市值(万元)',
            net_value_ratio DECIMAL(10,4) NULL COMMENT '占净值比(%)',
            stock_invest_ratio DECIMAL(10,4) NULL COMMENT '占股票投资市值比(%)',
            standard_allocation_ratio DECIMAL(10,4) NULL COMMENT '股票市场标准行业配置比例(%)',
            relative_allocation_ratio DECIMAL(10,4) NULL COMMENT '相对标准行业配置比例(%)',
            market_value_growth_rate DECIMAL(10,4) NULL COMMENT '市值增长率(%)',
            investment_type VARCHAR(100) NULL COMMENT '投资类型',
            management_company VARCHAR(200) NULL COMMENT '管理公司',
            update_time DATETIME NOT NULL COMMENT '表格更新时间',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            
            PRIMARY KEY (fund_code, industry_code),
            INDEX idx_fund_code (fund_code),
            INDEX idx_industry_code (industry_code),
            INDEX idx_industry_name (industry_name),
            INDEX idx_management_company (management_company),
            INDEX idx_update_time (update_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
        COMMENT='基金行业配置表';
    `;

    try {
        await pool.execute(createTableSQL);
        console.log('✅ 表结构检查/创建完成');
    } catch (error) {
        console.error('❌ 创建表失败:', error.message);
        throw error;
    }
}

/**
 * 数据验证和清洗
 * @param {Object} record - 原始记录
 * @returns {Object|null} 清洗后的记录
 */
function validateAndCleanRecord(record) {
    try {
        // 必填字段检查
        if (!record.fundCode || !record.industryCode || !record.fundName || !record.industryName) {
            console.warn('⚠️ 跳过无效记录：缺少必填字段', {
                fundCode: record.fundCode,
                industryCode: record.industryCode
            });
            return null;
        }

        // 数据清洗和转换
        const cleanedRecord = {
            fund_code: String(record.fundCode).trim(),
            industry_code: String(record.industryCode).trim(),
            fund_name: String(record.fundName).trim(),
            industry_name: String(record.industryName).trim(),
            market_value: record.marketValue !== null ? Number(record.marketValue) : null,
            net_value_ratio: record.netValueRatio !== null ? Number(record.netValueRatio) : null,
            stock_invest_ratio: record.stockInvestRatio !== null ? Number(record.stockInvestRatio) : null,
            standard_allocation_ratio: record.standardAllocationRatio !== null ? Number(record.standardAllocationRatio) : null,
            relative_allocation_ratio: record.relativeAllocationRatio !== null ? Number(record.relativeAllocationRatio) : null,
            market_value_growth_rate: record.marketValueGrowthRate !== null ? Number(record.marketValueGrowthRate) : null,
            investment_type: record.investmentType ? String(record.investmentType).trim() : null,
            management_company: record.managementCompany ? String(record.managementCompany).trim() : null,
            update_time: new Date(record.updateTime)
        };

        return cleanedRecord;
    } catch (error) {
        console.warn('⚠️ 数据清洗失败:', error.message, record);
        return null;
    }
}

/**
 * 批量插入数据
 * @param {mysql.Pool} pool - 数据库连接池
 * @param {Array} records - 记录数组
 * @param {number} batchNumber - 批次号
 * @returns {Promise<number>} 成功插入的记录数
 */
async function batchInsertRecords(pool, records, batchNumber) {
    if (records.length === 0) {
        return 0;
    }

    const insertSQL = `
        INSERT INTO tonghuashun_fund_industry_info (
            fund_code, industry_code, fund_name, industry_name,
            market_value, net_value_ratio, stock_invest_ratio,
            standard_allocation_ratio, relative_allocation_ratio,
            market_value_growth_rate, investment_type, management_company,
            update_time
        ) VALUES ?
        ON DUPLICATE KEY UPDATE
            fund_name = VALUES(fund_name),
            industry_name = VALUES(industry_name),
            market_value = VALUES(market_value),
            net_value_ratio = VALUES(net_value_ratio),
            stock_invest_ratio = VALUES(stock_invest_ratio),
            standard_allocation_ratio = VALUES(standard_allocation_ratio),
            relative_allocation_ratio = VALUES(relative_allocation_ratio),
            market_value_growth_rate = VALUES(market_value_growth_rate),
            investment_type = VALUES(investment_type),
            management_company = VALUES(management_company),
            update_time = VALUES(update_time),
            updated_at = CURRENT_TIMESTAMP
    `;

    const values = records.map(record => [
        record.fund_code,
        record.industry_code,
        record.fund_name,
        record.industry_name,
        record.market_value,
        record.net_value_ratio,
        record.stock_invest_ratio,
        record.standard_allocation_ratio,
        record.relative_allocation_ratio,
        record.market_value_growth_rate,
        record.investment_type,
        record.management_company,
        record.update_time
    ]);

    try {
        const [result] = await pool.execute(insertSQL, [values]);
        console.log(`✅ 批次 ${batchNumber}: 成功处理 ${records.length} 条记录 (插入: ${result.affectedRows - result.changedRows}, 更新: ${result.changedRows})`);
        return result.affectedRows;
    } catch (error) {
        console.error(`❌ 批次 ${batchNumber} 插入失败:`, error.message);
        throw error;
    }
}

/**
 * 读取JSON数据文件
 * @param {string} filePath - JSON文件路径
 * @returns {Promise<Array>} 数据数组
 */
async function readJsonData(filePath) {
    try {
        if (!fs.existsSync(filePath)) {
            throw new Error(`文件不存在: ${filePath}`);
        }

        console.log(`📖 正在读取数据文件: ${filePath}`);
        const fileContent = fs.readFileSync(filePath, 'utf8');
        const data = JSON.parse(fileContent);

        if (!Array.isArray(data)) {
            throw new Error('JSON文件格式错误：期望数组格式');
        }

        console.log(`✅ 成功读取 ${data.length} 条记录`);
        return data;
    } catch (error) {
        console.error('❌ 读取JSON文件失败:', error.message);
        throw error;
    }
}

/**
 * 主导入函数
 * @param {string} jsonFilePath - JSON数据文件路径
 */
async function importFundIndustryData(jsonFilePath) {
    let pool = null;
    let totalProcessed = 0;
    let totalSuccess = 0;
    let totalErrors = 0;

    try {
        console.log('🚀 开始导入基金行业配置数据');
        console.log(`导入时间: ${new Date().toLocaleString()}`);
        console.log('='.repeat(60));

        // 初始化数据库连接
        pool = await initializeDatabase();

        // 创建表结构
        await createTableIfNotExists(pool);

        // 读取JSON数据
        const rawData = await readJsonData(jsonFilePath);

        // 数据验证和清洗
        console.log('🔄 开始数据验证和清洗...');
        const validRecords = [];

        for (let i = 0; i < rawData.length; i++) {
            const cleanedRecord = validateAndCleanRecord(rawData[i]);
            if (cleanedRecord) {
                validRecords.push(cleanedRecord);
            } else {
                totalErrors++;
            }

            // 进度显示
            if ((i + 1) % 10000 === 0) {
                console.log(`📊 已验证 ${i + 1}/${rawData.length} 条记录`);
            }
        }

        console.log(`✅ 数据验证完成: 有效记录 ${validRecords.length} 条，无效记录 ${totalErrors} 条`);
        console.log('='.repeat(60));

        // 批量导入数据
        console.log('🔄 开始批量导入数据...');
        const batchSize = IMPORT_CONFIG.batchSize;
        const totalBatches = Math.ceil(validRecords.length / batchSize);

        for (let i = 0; i < validRecords.length; i += batchSize) {
            const batch = validRecords.slice(i, i + batchSize);
            const batchNumber = Math.floor(i / batchSize) + 1;

            try {
                const insertedCount = await batchInsertRecords(pool, batch, batchNumber);
                totalSuccess += insertedCount;
                totalProcessed += batch.length;

                // 进度显示
                const progress = ((batchNumber / totalBatches) * 100).toFixed(1);
                console.log(`📈 进度: ${progress}% (${batchNumber}/${totalBatches} 批次)`);

            } catch (error) {
                console.error(`❌ 批次 ${batchNumber} 处理失败:`, error.message);
                totalErrors += batch.length;

                // 根据配置决定是否继续
                if (IMPORT_CONFIG.maxRetries > 0) {
                    console.log(`🔄 将在 ${IMPORT_CONFIG.retryDelay}ms 后重试...`);
                    await new Promise(resolve => setTimeout(resolve, IMPORT_CONFIG.retryDelay));
                }
            }
        }

        console.log('='.repeat(60));
        console.log('📊 导入完成统计:');
        console.log(`   总记录数: ${rawData.length}`);
        console.log(`   有效记录: ${validRecords.length}`);
        console.log(`   成功导入: ${totalSuccess}`);
        console.log(`   失败记录: ${totalErrors}`);
        console.log(`   成功率: ${((totalSuccess / rawData.length) * 100).toFixed(2)}%`);
        console.log('='.repeat(60));
        console.log('✅ 数据导入完成！');

    } catch (error) {
        console.error('❌ 导入过程发生错误:', error.message);
        console.error('错误详情:', error);
        throw error;
    } finally {
        // 关闭数据库连接
        if (pool) {
            await pool.end();
            console.log('🔒 数据库连接已关闭');
        }
    }
}

/**
 * 主执行函数
 */
async function main() {
    try {
        // 检查环境变量
        console.log('🔧 数据库配置检查:');
        console.log(`   主机: ${DB_CONFIG.host}:${DB_CONFIG.port}`);
        console.log(`   数据库: ${DB_CONFIG.database}`);
        console.log(`   用户: ${DB_CONFIG.user}`);
        console.log('='.repeat(60));

        // JSON文件路径
        const jsonFilePath = path.join(process.cwd(), 'data', 'fund_industry_info.json');

        // 开始导入
        await importFundIndustryData(jsonFilePath);

    } catch (error) {
        console.error('❌ 程序执行失败:', error.message);
        process.exit(1);
    }
}

// 执行脚本
if (require.main === module) {
    main();
}

module.exports = {
    importFundIndustryData,
    initializeDatabase,
    createTableIfNotExists,
    validateAndCleanRecord,
    batchInsertRecords
};
